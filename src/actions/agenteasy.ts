import {
  IAgentEasyChatResponse,
  IAgentEasyConversationDetailsResponse,
} from 'src/types/agenteasy';
import { API_ENDPOINTS, fetchRequest } from '.';

export const getAgentEasyChatMessages = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<IAgentEasyChatResponse> => {
  const url = new URL(API_ENDPOINTS.AGENTEASY_CHAT_MESSAGES);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<IAgentEasyChatResponse>;
  return response;
};

export const getAgentEasyConversationDetails = async ({
  headers,
  queryParams = {},
}: {
  headers: Record<string, string>;
  queryParams: Record<string, string>;
}): Promise<IAgentEasyConversationDetailsResponse> => {
  const url = new URL(API_ENDPOINTS.AGENTEASY_CONVERSATION_DETAILS);
  Object.keys(queryParams).forEach((key) => {
    url.searchParams.set(key, queryParams[key]);
  });
  const response = (await fetchRequest({
    url: url.toString(),
    headers,
  })) as Promise<IAgentEasyConversationDetailsResponse>;
  return response;
};
