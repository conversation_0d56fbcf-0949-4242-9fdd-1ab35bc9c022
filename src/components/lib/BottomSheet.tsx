import { ReactNode } from 'react';
import HorizontalLoader from './HorizontalLoader';

interface IBottomSheetProps {
  className?: string;
  children: ReactNode;
  onClose?: () => void;
  loading?: boolean;
  contentClassName?: string;
}

const BottomSheet = (props: IBottomSheetProps) => {
  const {
    className = '',
    children,
    onClose = () => {},
    loading,
    contentClassName = '',
  } = props;

  return (
    <div className="fixed w-full h-full top-0 left-0 right-0 bottom-0 flex items-center justify-center z-50">
      <div
        className="fixed w-full h-full top-0 left-0 right-0 bottom-0 bg-black opacity-70"
        onClick={onClose}
      ></div>
      <div
        className={`fixed bottom-0 sm:bottom-6 bg-white rounded-t-2xl sm:rounded-b-2xl max-h-4/5 overflow-scroll animate-ModalB2T w-full z-50 ${className}`}
        style={{ maxWidth: 550 }}
      >
        {loading ? <HorizontalLoader /> : null}
        <div className={`py-6 px-4 relative z-50 ${contentClassName}`}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default BottomSheet;
