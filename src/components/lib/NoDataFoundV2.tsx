import { BsGraphUp } from 'react-icons/bs';

const NoDataFoundV2 = ({ className }: { className?: string }) => {
  return (
    <div className={className}>
      <div className="flex items-center justify-center my-6 text-center border-2 border-dashed border-gray-200 rounded-xl p-8 max-w-md w-full flex-col mx-auto">
        <div className="mx-auto mb-3 w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
          <BsGraphUp className="text-gray-400" size={24} />
        </div>
        <p className="text-base font-medium text-gray-700">
          No insights available
        </p>
        <p className="mt-1 text-sm text-gray-500">
          We couldn’t find amount spend data and insights for this period.
        </p>
      </div>
    </div>
  );
};

export default NoDataFoundV2;
