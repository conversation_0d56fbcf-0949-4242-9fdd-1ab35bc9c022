import CampaignLeadsListItem from '@/components/campaign_details/CampaignLeadsListItem';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { IPartnerConfig } from 'src/types';
import { ICampaign } from 'src/types/campaigns';
import { ICtwaLead, IMetaLead } from 'src/types/leads';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import { getParseLead } from 'src/utils';
import NoDataFound from '../../lib/NoDataFound';
import FilterButtons, { ILeadFilters } from './FilterButtons';

interface ILeadsListProps {
  data: Array<
    (IMetaLead | ICtwaLead) & {
      crm_details?: ILeadsCrmDetails;
      campaign_details?: Partial<ICampaign>;
    }
  >;
  onNextPress: () => void;
  noMoreLeadsAvailable: boolean;
  loading: boolean;
  campaigns: ICampaign[];
  onEditLeadsCrmClick: (details: Partial<ILeadsCrmDetails>) => void;
  partnerConfig?: IPartnerConfig;
  onFiltersChange?: (filters: ILeadFilters) => void;
  activeFilters: ILeadFilters;
}

const LeadsList = (props: ILeadsListProps) => {
  const {
    data,
    onNextPress,
    noMoreLeadsAvailable,
    loading,
    campaigns,
    onEditLeadsCrmClick,
    partnerConfig,
    onFiltersChange,
    activeFilters,
  } = props;

  const handleFiltersChange = (newFilters: ILeadFilters) => {
    // Pass filters to parent component for API filtering
    if (onFiltersChange) {
      onFiltersChange(newFilters);
    }
  };

  return (
    <div className="flex flex-col flex-1 w-full h-full">
      <FilterButtons
        campaigns={campaigns}
        onFiltersChange={handleFiltersChange}
        activeFilters={activeFilters}
      />

      {!data.length ? (
        <NoDataFound
          illustration={{
            url: '/images/dashboard/leads-illustration.svg',
            width: 200,
            height: 200,
          }}
          title="No Leads Found!"
          imageClassName="rounded-xl"
        />
      ) : (
        <>
          <div className="flex flex-col flex-1 overflow-scroll no-scrollbar">
            {data.map((item, index) => {
              const campaignDetails =
                item.campaign_details ||
                campaigns?.find((campaignItem) => {
                  if ('form_id' in item) {
                    return campaignItem.meta_leadgen_form_id === item.form_id;
                  } else {
                    return campaignItem.id === item.campaign_id;
                  }
                });
              const campaignName =
                campaignDetails?.friendly_name ??
                campaignDetails?.details?.business_details?.business_category ??
                'NA';
              return (
                <div key={index} className="mt-5 first:mt-0">
                  <p className="text-sm text-gray-dark font-medium">
                    {campaignName}
                  </p>
                  <CampaignLeadsListItem
                    data={getParseLead(
                      item,
                      campaignDetails?.details?.leadgen_form?.questions ?? [],
                    )}
                    className="!mt-2"
                    campaignName={campaignDetails?.name}
                    leadsCrmDetails={item.crm_details}
                    onEditLeadsCrmClick={(leadCrmDetails) => {
                      leadCrmDetails.campaign_id = campaignDetails?.id;
                      onEditLeadsCrmClick(leadCrmDetails);
                    }}
                    partnerConfig={partnerConfig}
                  />
                </div>
              );
            })}
          </div>
          {noMoreLeadsAvailable ? null : (
            <div className="my-3">
              <p
                className="text-sm text-hyperlink text-center cursor-pointer"
                onClick={onNextPress}
              >
                Load More
              </p>
            </div>
          )}
          {loading ? (
            <div className="flex justify-center mt-2">
              <SpinnerLoader size={24} borderWidth={2} />
            </div>
          ) : null}
        </>
      )}
    </div>
  );
};

export default LeadsList;
