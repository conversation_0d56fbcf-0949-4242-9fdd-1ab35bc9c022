import React, { useState, useRef } from 'react';
import { BiCalendar } from 'react-icons/bi';

interface Question {
  question: string;
  answer?: string;
  date?: string;
}

interface QuestionCarouselProps {
  questions: Question[];
}

const QuestionCarousel: React.FC<QuestionCarouselProps> = ({ questions }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const handleScroll = () => {
    const container = containerRef.current;
    if (!container) return;
    const idx = Math.round(container.scrollLeft / container.clientWidth);
    if (idx !== currentIndex) setCurrentIndex(idx);
  };

  if (!questions.length) return null;

  return (
    <div className="relative flex flex-col items-center w-full">
      <div
        ref={containerRef}
        onScroll={handleScroll}
        className="w-full md:w-[98%] flex overflow-x-auto scroll-smooth snap-x snap-mandatory no-scrollbar gap-2"
        style={{ WebkitOverflowScrolling: 'touch' }}
      >
        {questions.map((q, idx) => (
          <div
            key={idx}
            className="flex-shrink-0 w-full md:w-[98%] bg-white rounded-xl border border-gray-200 p-4  transition-all duration-200 snap-center shadow-sm"
          >
            <p className="text-sm text-gray-600 mb-2">{q.question}</p>
            <p className="text-base font-semibold text-primary2">
              {q.answer || 'No answer provided'}
            </p>

            {q.date && (
              <div className="flex items-center text-xs text-gray-500">
                <BiCalendar size={14} className="mr-1" />
                <span>{q.date}</span>
              </div>
            )}
          </div>
        ))}
      </div>

      {questions.length > 1 && (
        <div className="flex justify-center mt-2 space-x-2 pointer-events-none">
          {questions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? 'bg-primary2'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default QuestionCarousel;
