import { useEffect } from 'react';
import { IGroweasyUser } from 'src/types';
import { ICampaign } from 'src/types/campaigns';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import CampaignsList from '@/components/dashboard/campaigns/CampaignsList';
import { UseInfiniteQueryResult } from 'react-query';
import FetchError from 'src/actions/FetchError';

interface IDashboardCampaignsProps {
  user?: IGroweasyUser;
  campaigns: ICampaign[];
  campaignsResponse: UseInfiniteQueryResult<
    { data: { campaigns: ICampaign[]; last_cursor_id: string } },
    FetchError | Error
  >;
  onCampaignsChange: (campaigns: ICampaign[]) => void;
  onCreateCampaignClick: () => void;
}

const DashboardCampaigns = (props: IDashboardCampaignsProps) => {
  const {
    campaigns,
    campaignsResponse,
    onCampaignsChange,
    onCreateCampaignClick,
  } = props;

  useEffect(() => {
    onCampaignsChange(campaigns);
  }, [campaigns, onCampaignsChange]);

  if (campaignsResponse.isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <SpinnerLoader size={40} />
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-1 mt-6">
      <CampaignsList
        data={campaigns}
        onCreateCampaignClick={onCreateCampaignClick}
      />
      {campaignsResponse.hasNextPage ? (
        <div className="my-3">
          {campaignsResponse.isFetching ? (
            <div className="flex justify-center">
              <SpinnerLoader borderWidth={2} size={20} />
            </div>
          ) : (
            <p
              className="text-sm text-center text-hyperlink cursor-pointer"
              onClick={() => {
                void campaignsResponse.fetchNextPage();
              }}
            >
              Load More
            </p>
          )}
        </div>
      ) : null}
    </div>
  );
};

export default DashboardCampaigns;
