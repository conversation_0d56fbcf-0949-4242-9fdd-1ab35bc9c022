import { useState, useMemo } from 'react';
import { useRouter } from 'next/router';
import { useInfiniteQuery } from 'react-query';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import { ICampaign } from 'src/types/campaigns';
import { IMetaLead } from 'src/types/leads';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import { QueryParams } from 'src/constants';
import { getAllFormLeads } from 'src/actions/dashboard';
import { getCommonHeaders } from 'src/actions';
import FetchError from 'src/actions/FetchError';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import LeadsList from '@/components/dashboard/leads/LeadsList';
import EditLeadsCrmDetailsBs from '@/components/campaign_details/bottom_sheets/EditLeadsCrmDetailsBs';
import { ILeadFilters } from '@/components/dashboard/leads/FilterButtons';

interface IDashboardLeadsProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
  campaigns: ICampaign[];
}

const DashboardLeads = (props: IDashboardLeadsProps) => {
  const { user, partnerConfig, campaigns } = props;
  const router = useRouter();

  // Leads filtering and CRM state
  const [leadFilters, setLeadFilters] = useState<ILeadFilters>({});
  const [selectedLeadsCrmDetails, setSelectedLeadsCrmDetails] =
    useState<Partial<ILeadsCrmDetails> | null>(null);

  // Convert filters to query params
  const getFilterQueryParams = () => {
    const params: Record<string, string> = {};

    if (leadFilters.dateRange) {
      const predefinedRanges = [
        'today',
        'yesterday',
        'last_7_days',
        'last_30_days',
      ];
      const isCustomDateRange = !predefinedRanges.includes(
        leadFilters.dateRange.start,
      );

      if (isCustomDateRange) {
        const startDate = new Date(leadFilters.dateRange.start + 'T00:00:00');
        const endDate = new Date(leadFilters.dateRange.end + 'T23:59:59');
        params.created_time_from = Math.floor(
          startDate.getTime() / 1000,
        ).toString();
        params.created_time_to = Math.floor(
          endDate.getTime() / 1000,
        ).toString();
      } else {
        const today = new Date();
        let startDate = new Date();
        let endDate = new Date();

        switch (leadFilters.dateRange.start) {
          case 'today':
            startDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate(),
            );
            endDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate(),
              23,
              59,
              59,
            );
            break;
          case 'yesterday':
            startDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate() - 1,
            );
            endDate = new Date(
              today.getFullYear(),
              today.getMonth(),
              today.getDate() - 1,
              23,
              59,
              59,
            );
            break;
          case 'last_7_days':
            startDate = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000);
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date();
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'last_30_days':
            startDate = new Date(today.getTime() - 29 * 24 * 60 * 60 * 1000);
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date();
            endDate.setHours(23, 59, 59, 999);
            break;
        }

        params.created_time_from = Math.floor(
          startDate.getTime() / 1000,
        ).toString();
        params.created_time_to = Math.floor(
          endDate.getTime() / 1000,
        ).toString();
      }
    }

    if (leadFilters.campaign) {
      params.campaign_ids = leadFilters.campaign;
    }

    if (leadFilters.quality) {
      params.lead_category = leadFilters.quality;
    }

    return params;
  };

  const allFormLeadsResponse = useInfiniteQuery(
    ['getAllFormLeads', JSON.stringify(leadFilters)],
    (params) => {
      const queryParams: Record<string, string> = {
        ...router.query,
        ...getFilterQueryParams(),
        [QueryParams.LIMIT]: '20',
      } as Record<string, string>;

      if (params.pageParam) {
        queryParams[QueryParams.START_AFTER] = params.pageParam as string;
      }

      return getAllFormLeads({
        headers: getCommonHeaders(user),
        queryParams,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        const leads = lastPage.data;
        if (leads?.length && leads.length === 20) {
          const lastLead = leads[leads.length - 1];
          return `${lastLead.created_time}`;
        }
        return undefined;
      },
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'DashboardLeads.getAllFormLeads');
      },
    },
  );

  const allLeads: Array<IMetaLead & { crm_details?: ILeadsCrmDetails }> =
    useMemo(() => {
      const result: Array<IMetaLead & { crm_details?: ILeadsCrmDetails }> = [];
      allFormLeadsResponse.data?.pages?.forEach((page) => {
        result.push(...page.data);
      });
      return result.sort(
        (a, b) => (b.created_time as number) - (a.created_time as number),
      );
    }, [allFormLeadsResponse.data?.pages]);

  const handleLeadFiltersChange = (filters: ILeadFilters) => {
    setLeadFilters(filters);
  };

  // details will have campaign_id from child
  const onEditLeadsCrmClick = (details: Partial<ILeadsCrmDetails>) => {
    details.uid = user?.uid;
    setSelectedLeadsCrmDetails(details);
  };

  const onLeadsCrmDetailsUpdate = () => {
    void allFormLeadsResponse.refetch();
  };

  const onNextLeadsPress = () => {
    if (allFormLeadsResponse.hasNextPage && !allFormLeadsResponse.isFetching) {
      void allFormLeadsResponse.fetchNextPage();
    }
  };

  if (allFormLeadsResponse.isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <SpinnerLoader size={40} />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full flex-1">
      <div className="flex flex-col flex-1 items-center overflow-hidden">
        <LeadsList
          data={allLeads}
          onNextPress={onNextLeadsPress}
          noMoreLeadsAvailable={!allFormLeadsResponse.hasNextPage}
          loading={allFormLeadsResponse.isFetching}
          campaigns={campaigns}
          onEditLeadsCrmClick={onEditLeadsCrmClick}
          partnerConfig={partnerConfig}
          onFiltersChange={handleLeadFiltersChange}
          activeFilters={leadFilters}
        />
      </div>
      {selectedLeadsCrmDetails ? (
        <EditLeadsCrmDetailsBs
          leadsCrmDetails={selectedLeadsCrmDetails}
          onClose={() => setSelectedLeadsCrmDetails(null)}
          user={user}
          onUpdateDone={onLeadsCrmDetailsUpdate}
        />
      ) : null}
    </div>
  );
};

export default DashboardLeads;
