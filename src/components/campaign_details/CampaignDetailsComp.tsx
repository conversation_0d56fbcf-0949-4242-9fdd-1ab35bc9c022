import Image from 'next/image';
import { AdPlatforms, IGroweasyUser } from 'src/types';
import {
  GROWEASY_CAMPAIGN_TYPE,
  GrowEasyCampaignStatus,
  IAdCopy,
  IAdVideoDetails,
  ICampaign,
  ICampaignConfig,
  ICampaignInsightDetails,
} from 'src/types/campaigns';
import { getGenderDetailsString } from 'src/utils';
import CampaignProgressBarComp from '../dashboard/campaigns/CampaignProgressBarComp';
import SelectedGeoLocations from '../onboarding/target_audience/SelectedGeoLocations';
import CampaignInsightsComp from './CampaignInsightsComp';
import LeadsQualifyingQuestionsAccordion from './LeadsQualifyingQuestionsAccordion';
import PostAdOnFacebookPage from './PostAdOnFacebookPage';
import { GROWEASY_DEFAULT_PAGE_ID } from 'src/constants';
import { ReactNode } from 'react';
import SpinnerLoader from '../lib/SpinnerLoader';
import GoogleSelectedGeoLocations from './google/GoogleSelectedGeoLocations';
import KeywordsAccordion from './google/KeywordsAccordion';
import PixelSetupAccordion from './PixelSetupAccordion';
import SalesLandingPageUrlComp from './SalesLandingPageUrlComp';
import SalesTagSetupAccordion from './google/SalesTagSetupAccordion';
import LeadTagSetupAccordion from './google/LeadTagSetupAccordion';
import AdInsightCardsContainer from './AdInsightCardsContainer';
import GoogleAdPreviewsComp from './google/GoogleAdPreviewsComp';

interface ICampaignLeadsTabProps {
  campaignDetails: ICampaign;
  adImages?: Array<{
    permalink_url: string;
    width: number;
    height: number;
    url: string;
    hash: string;
  }>;
  className?: string;
  campaignInsightsDetails?: ICampaignInsightDetails;
  onEditLeadgenFormClick?: () => void;
  user: IGroweasyUser;
  onPagePostCreationSuccess?: (
    campaignConfig: Partial<ICampaignConfig>,
  ) => void;
  adVideos?: IAdVideoDetails[];
}

const CampaignDetailsComp = (props: ICampaignLeadsTabProps) => {
  const {
    campaignDetails,
    adImages,
    className = '',
    campaignInsightsDetails,
    onEditLeadgenFormClick,
    user,
    onPagePostCreationSuccess,
    adVideos,
  } = props;

  const fbPageId = campaignDetails?.details?.config?.fb_page_id;
  const enablePostingAdOnFbPage = false;

  const renderAdPreview = (adCopy: IAdCopy, Component: ReactNode) => {
    return (
      <>
        <p className="text-xs">{adCopy.primary_text}</p>
        <div className="mt-2">{Component}</div>
        <div className="flex items-center p-2 bg-gray-light/40 border-b border-b-gray-light">
          <p className="text-xs font-semibold flex-1 mr-4">{adCopy.headline}</p>
          <button className="bg-gray-light py-2 px-4 rounded-md text-xs font-semibold">
            {[
              GROWEASY_CAMPAIGN_TYPE.META_SALES,
              GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
            ].includes(campaignDetails?.type)
              ? 'Shop Now'
              : 'Learn More'}
          </button>
        </div>
      </>
    );
  };

  const renderAdVideos = () => {
    const adCopies = campaignDetails?.details?.ad_copies ?? [];
    if (!adCopies?.length) {
      return null;
    }
    let thumbnailUrl = '';
    let videoUrl = campaignDetails?.details?.ad_videos?.[0]?.video_url;
    if (adVideos?.length) {
      thumbnailUrl = adVideos[0].thumbnails?.data?.find(
        (thumbnail) => thumbnail.is_preferred,
      )?.uri;
      videoUrl = campaignDetails?.details?.ad_videos?.find(
        (video) => video.id === adVideos[0].id,
      )?.video_url;
    }
    if (videoUrl) {
      return (
        <div className="shrink-0 mr-6 w-60">
          {renderAdPreview(
            adCopies[0],
            <video
              width="280"
              controls
              poster={thumbnailUrl}
              className="rounded-md"
              autoFocus
              muted
              playsInline
              autoPlay
            >
              <source src={videoUrl} />
            </video>,
          )}
        </div>
      );
    }
    return null;
  };

  const connectformLeadForm =
    campaignDetails.google_ads_data?.lead_form_url?.includes(
      'connectform.co/lead-forms',
    );

  return (
    <div className={`flex flex-col ${className}`}>
      {adVideos?.length || adImages?.length ? (
        <div className="p-4 bg-white rounded-lg shadow flex items-stretch overflow-x-scroll no-scrollbar">
          {renderAdVideos()}
          {adImages
            ?.filter((item) => {
              // filter hidden images
              const adBanner = campaignDetails?.details?.ad_banners?.find(
                (banner) => banner.image?.hash === item.hash,
              );
              if (adBanner?.hidden) {
                return false;
              }
              return true;
            })
            // in reverse order of 1600 x 900, 1080 x 1080, 1080 x 1920, i.e. longest image first
            ?.sort((a, b) => {
              const aspectRatioA = a.width / a.height;
              const aspectRatioB = b.width / b.height;
              return aspectRatioA - aspectRatioB;
            })
            ?.map((item, index) => {
              const adCopies = campaignDetails?.details?.ad_copies ?? [];
              const adCopy = adCopies[index % adCopies.length];
              if (!adCopy) {
                return null;
              }
              return (
                <div key={index} className="shrink-0 mr-6 w-60 overflow-hidden">
                  {renderAdPreview(
                    adCopy,
                    <Image
                      src={item.url}
                      width={item.width}
                      height={item.height}
                      alt=""
                      className="bg-gray-light"
                    />,
                  )}
                </div>
              );
            })}
        </div>
      ) : [
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
        ].includes(campaignDetails?.type) ? (
        <GoogleAdPreviewsComp
          googleAdsData={campaignDetails.google_ads_data}
          type={campaignDetails.type}
          campaignDetails={campaignDetails}
        />
      ) : (
        <div className="p-4 bg-white rounded-lg shadow flex items-center justify-center">
          <SpinnerLoader />
        </div>
      )}
      <div className="mt-4 p-4 bg-white rounded-lg shadow">
        {campaignDetails.platform === AdPlatforms.GOOGLE ? (
          <GoogleSelectedGeoLocations
            geoLocations={campaignDetails.google_ads_data?.geo_locations}
          />
        ) : (
          <SelectedGeoLocations
            geoLocations={campaignDetails.details?.targeting?.geo_locations}
          />
        )}
        <p className="text-xs !font-medium mt-2">
          {`${campaignDetails.details?.targeting?.age_min} - ${campaignDetails.details?.targeting?.age_max} years`}
          ,{' '}
          {getGenderDetailsString(campaignDetails.details?.targeting?.genders)}
        </p>
        <CampaignProgressBarComp
          campaignDetails={campaignDetails}
          className="mt-3"
        />
      </div>

      {campaignInsightsDetails ? (
        <CampaignInsightsComp
          data={campaignInsightsDetails}
          className="mt-4"
          budgetAndScheduling={campaignDetails?.details?.budget_and_scheduling}
          campaignType={campaignDetails.type}
        />
      ) : null}

      {[
        GROWEASY_CAMPAIGN_TYPE.CTWA,
        GROWEASY_CAMPAIGN_TYPE.META_SALES,
        GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
        GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
      ].includes(campaignDetails.type) ||
      (campaignDetails.google_ads_data?.lead_form_url &&
        !campaignDetails.google_ads_data?.lead_form_url?.includes(
          'connectform.co/lead-forms',
        )) ? null : (
        <LeadsQualifyingQuestionsAccordion
          leadgenForm={campaignDetails?.details?.leadgen_form}
          onEditCtaClick={onEditLeadgenFormClick}
          campaignStatus={campaignDetails?.status}
          googleAdsData={campaignDetails?.google_ads_data}
          adPlatform={campaignDetails?.platform}
        />
      )}

      {[
        GROWEASY_CAMPAIGN_TYPE.META_SALES,
        GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
      ].includes(campaignDetails.type) ? (
        <SalesLandingPageUrlComp
          url={campaignDetails?.details?.business_details?.website}
        />
      ) : null}

      {campaignDetails?.platform === AdPlatforms.GOOGLE ? (
        <>
          {campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX ? (
            <SalesTagSetupAccordion
              googleAdsData={campaignDetails?.google_ads_data}
            />
          ) : connectformLeadForm ? null : (
            <LeadTagSetupAccordion
              googleAdsData={campaignDetails?.google_ads_data}
            />
          )}
        </>
      ) : null}

      {campaignDetails.type === GROWEASY_CAMPAIGN_TYPE.META_SALES ? (
        <PixelSetupAccordion campaignDetails={campaignDetails} />
      ) : null}

      {campaignDetails.platform === AdPlatforms.GOOGLE ? (
        <KeywordsAccordion googleAdsData={campaignDetails.google_ads_data} />
      ) : null}

      {campaignDetails.platform === AdPlatforms.META &&
      campaignDetails.status !== GrowEasyCampaignStatus.DRAFT ? (
        <AdInsightCardsContainer
          user={user}
          campaignDetails={campaignDetails}
        />
      ) : null}

      {onPagePostCreationSuccess &&
      fbPageId &&
      fbPageId !== GROWEASY_DEFAULT_PAGE_ID &&
      adImages?.[0]?.url &&
      enablePostingAdOnFbPage ? (
        <PostAdOnFacebookPage
          campaignDetails={campaignDetails}
          user={user}
          photoUrl={
            adImages?.filter((item) => item.height === item.width)[0].url
          }
          onPagePostCreationSuccess={onPagePostCreationSuccess}
          className="mt-4"
        />
      ) : null}
    </div>
  );
};

export default CampaignDetailsComp;
