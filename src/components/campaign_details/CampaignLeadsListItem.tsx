import { IParsedLead } from 'src/types/leads';
import EmailIcon from '@/images/common/email.svg';
import PhoneIcon from '@/images/common/phone.svg';
import Image from 'next/image';
import { getFormattedTimeString, shareText } from 'src/utils';
import Accordion from '../lib/Accordion';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import EditIcon from '@/images/common/edit.svg';
import { ILeadsCrmDetails, LeadStatusNameMapping } from 'src/types/leads_crm';
import { GrowEasyPartners, IPartnerConfig } from 'src/types';
import { BiChevronRight } from 'react-icons/bi';
import { useRouter } from 'next/router';
import QuestionCarousel from '../dashboard/leads/QuestionsCarousel';

interface ICampaignLeadsListItemProps {
  data: IParsedLead;
  className?: string;
  campaignName: string;
  leadsCrmDetails?: ILeadsCrmDetails;
  onEditLeadsCrmClick?: (leadsCrmDetails: Partial<ILeadsCrmDetails>) => void;
  partnerConfig?: IPartnerConfig;
}

const leadStatusColorMap = {
  CONTACTED: '#C9EBFF', // Light Blue
  IRRELEVANT: '#FFE5E5', // Light Red
  DID_NOT_CONNECT: '#FFDAB9', // Peach
  FOLLOW_UP: '#D9FFDB', // Light Green
  INTERESTED: '#FFECB3', // Light Yellow
  SALE_DONE: '#E0E0E0', // Light Gray
  NOT_INTERESTED: '#FFD1D1', // Light Pink
  YET_TO_CONTACT: '#F3F3F3', // Off-White
  // new status
  DID_NOT_CONNECT_OR_BUSY: '#FFDAB9', // Peach
  GOOD_LEAD_FOLLOW_UP: '#D9FFDB', // Light Green
  BAD_LEAD: '#FFE5E5', // Light Red
};

const leadNoteColorMap = {
  CONTACTED: '#004080', // Dark Blue
  IRRELEVANT: '#800000', // Dark Red
  DID_NOT_CONNECT: '#FF8C00', // Dark Orange
  FOLLOW_UP: '#006400', // Dark Green
  INTERESTED: '#8B4513', // Saddle Brown
  SALE_DONE: '#404040', // Dark Gray
  NOT_INTERESTED: '#800080', // Dark Purple
  YET_TO_CONTACT: '#333333', // Dark Charcoal
  // new status
  DID_NOT_CONNECT_OR_BUSY: '#FF8C00', // Dark Orange
  GOOD_LEAD_FOLLOW_UP: '#006400', // Dark Green
  BAD_LEAD: '#d10404', // Dark Red
};

const CampaignLeadsListItem = (props: ICampaignLeadsListItemProps) => {
  const {
    data,
    className = '',
    campaignName,
    leadsCrmDetails,
    onEditLeadsCrmClick,
    partnerConfig,
  } = props;

  const router = useRouter();

  const name =
    data.field_data?.find((fieldItem) => fieldItem.type === 'FULL_NAME')
      ?.values?.[0] ?? '';
  const email =
    data.field_data?.find((fieldItem) => fieldItem.type === 'EMAIL')
      ?.values?.[0] ?? '';
  const phone =
    data.field_data?.find((fieldItem) => fieldItem.type === 'PHONE')
      ?.values?.[0] ?? '';
  const createdTime =
    typeof data.created_time === 'string'
      ? data.created_time
      : data.created_time * 1000;

  const onShareIconClicked = () => {
    logEvent(EVENT_NAMES.lead_share_icon_clicked);
    let textToBeShared = `${
      partnerConfig?.partner === GrowEasyPartners.NIVIDA ? '' : campaignName
    }

Name: ${name}
Mobile: ${phone}
Email: ${email}
`;
    data.field_data
      ?.filter((fieldItem) => fieldItem.type === 'CUSTOM')
      .map((item, index) => {
        const value = item.values?.[0];
        textToBeShared += `
${index + 1}. ${item.label}: ${value}
`;
      });
    shareText(textToBeShared);
  };

  const onAiChatClicked = () => {
    logEvent(EVENT_NAMES.lead_ai_chat_clicked);
    if (data.agenteasy?.chat_doc_id) {
      void router.push(`/agenteasy/chat/${data.agenteasy.chat_doc_id}`);
    }
  };

  const statusColorCode = leadStatusColorMap[leadsCrmDetails?.status];
  const noteColorCode = leadNoteColorMap[leadsCrmDetails?.status];

  return (
    <div className={`mt-3 p-3 bg-white rounded-lg shadow ${className}`}>
      <div className="flex items-center flex-1">
        <div className="w-full">
          <div className="flex items-center">
            <p className="text-sm font-medium mr-2">
              {data.navlink ? (
                <a
                  href={data.navlink}
                  target="_blank"
                  className="text-hyperlink"
                >
                  {name}
                </a>
              ) : (
                <span>{name}</span>
              )}
            </p>
            {leadsCrmDetails?.status && (
              <p
                className="mr-2 text-xs px-1 py-0.5 rounded"
                style={{
                  background: statusColorCode,
                }}
              >
                {LeadStatusNameMapping[leadsCrmDetails?.status] ??
                  leadsCrmDetails?.status?.replaceAll('_', ' ')}
              </p>
            )}
            {data?.agenteasy?.lead_category && (
              <p
                className={
                  'mr-2 text-xxs px-1 py-0.5 rounded text-white font-semibold' +
                  ` ${
                    data?.agenteasy?.lead_category === 'HOT'
                      ? 'bg-red'
                      : data?.agenteasy?.lead_category === 'COLD'
                        ? 'bg-blue-600'
                        : 'bg-amber-600'
                  }`
                }
              >
                {data?.agenteasy?.lead_category}
              </p>
            )}
            <div className="flex-1" />
            {onEditLeadsCrmClick ? (
              <div
                className="cursor-pointer"
                onClick={() =>
                  onEditLeadsCrmClick(
                    leadsCrmDetails ?? { leadgen_id: data.id },
                  )
                }
              >
                <EditIcon className="text-gray-dark" />
              </div>
            ) : null}
          </div>
          {email ? (
            <div className="flex items-center mt-2">
              <div className="text-gray-dark mr-1 w-3.5">
                <EmailIcon />
              </div>
              <p className="text-xs text-gray-dark">{email}</p>
            </div>
          ) : null}
          <div className="flex items-center mt-1">
            <div className="text-gray-dark mr-1 w-3.5 ">
              <PhoneIcon />
            </div>
            <p className="text-xs text-gray-dark">{phone}</p>
          </div>
        </div>
      </div>
      <div className="flex items-center mt-3">
        <a
          className="mr-2"
          href={`tel:${phone}`}
          onClick={() => logEvent(EVENT_NAMES.lead_call_icon_clicked)}
        >
          <Image
            src="/images/common/call-icon.png"
            width="28"
            height="28"
            alt=""
          />
        </a>
        <a
          className="mr-2"
          href={`https://wa.me/${phone}`}
          target="_blank"
          onClick={() => logEvent(EVENT_NAMES.lead_whatsapp_icon_clicked)}
        >
          <Image
            src="/images/common/whatsapp-icon.png"
            width="28"
            height="28"
            alt=""
          />
        </a>
        <div
          className="rounded-full border border-gray-light flex items-center justify-center mr-2"
          style={{ height: 28, width: 28 }}
        >
          <a href="#" onClick={onShareIconClicked}>
            <Image
              src="/images/common/share-icon.png"
              width="16"
              height="16"
              alt=""
            />
          </a>
        </div>
        {data.agenteasy?.chat_doc_id && (
          <div
            className="rounded-lg border border-primary2/50 bg-teal-700/5 flex items-center justify-center mr-2 text-xs py-1.5 px-4 cursor-pointer"
            onClick={onAiChatClicked}
          >
            {/* <div className="absolute -top-2 right-1 bg-red text-white text-[9px] px-1 rounded">
              New
            </div> */}
            Read Agent&apos;s Chat{' '}
            <BiChevronRight size={20} className="text-primary2" />
          </div>
        )}
      </div>
      <Accordion title="More Details" className="mt-3">
        <div>
          {/* Lead Summary Section */}
          {data.agenteasy?.lead_summary && (
            <div className="bg-teal-600/10 rounded-xl p-3 mb-4 border border-primary2/30">
              <p className="text-sm font-semibold text-primary mb-2">
                Lead Summary
              </p>
              <p className="text-xs text-gray-600 leading-relaxed mb-1">
                {data.agenteasy.lead_summary}
              </p>
            </div>
          )}

          {/* Questions Carousel */}
          {data.agenteasy?.asked_questions?.length && (
            <div className="mb-4">
              <p className="text-sm font-semibold text-gray-800 mb-3">
                Questions & Answers
              </p>
              <QuestionCarousel
                questions={data?.agenteasy?.asked_questions || []}
              />
            </div>
          )}
          <p className="mt-3 text-xs text-gray-dark">
            Received on: {getFormattedTimeString(new Date(createdTime))}
          </p>
          {leadsCrmDetails?.note ? (
            <div>
              <div className="my-3 w-full h-px bg-gray-light" />
              <p className="mt-3 text-xs text-gray-dark font-medium underline">
                Note
              </p>
              <p className="mt-2 text-xs" style={{ color: noteColorCode }}>
                {leadsCrmDetails.note}
              </p>
            </div>
          ) : null}
        </div>
      </Accordion>
    </div>
  );
};

export default CampaignLeadsListItem;
