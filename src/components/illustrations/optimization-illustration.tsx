import { BsClock } from 'react-icons/bs';

const barHeights = [45, 60, 35, 85, 50, 70, 55];

const OptimizationIllustration = () => {
  return (
    <div className="relative w-full h-full flex items-center justify-center cursor-pointer">
      <div className="w-full max-w-full aspect-[10/9] md:aspect-video relative mt-2">
        {/* Main card */}
        <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 rounded-xl overflow-hidden border border-gray-200">
          <div className="absolute inset-0 flex">
            {/* Left section - Charts */}
            <div className="flex-grow px-4 py-3 group">
              <div className="mb-2 flex justify-between items-center">
                <div className="flex items-center">
                  <div className="h-3 w-3 rounded-full bg-teal-500 mr-2 animate-pulse"></div>
                  <div className="text-[7px] md:text-xs font-medium text-gray-700">
                    Performance Tracking
                  </div>
                </div>
                <div className="text-[7px] md:text-xs bg-teal-50 border border-teal-100 rounded-md px-2 py-0.5 text-teal-700 flex items-center">
                  <div className="h-1.5 w-1.5 rounded-full bg-teal-500 mr-1 animate-pulse"></div>
                  Auto-optimizing
                </div>
              </div>

              {/* Main chart */}
              <div className="h-24 flex items-end gap-1 mt-2 border-b border-gray-200 relative">
                {barHeights.map((height, i) => (
                  <div key={i} className="flex-1 flex flex-col items-center">
                    <div
                      className={`
                        w-full rounded-t-sm transition-all duration-500
                        bg-teal-${
                          i % 3 === 0 ? '500' : i % 2 === 0 ? '400' : '300'
                        }
                        group-hover:scale-y-110
                        group-hover:bg-teal-500
                      `}
                      style={{
                        height: `${height}%`,
                        transformOrigin: 'bottom',
                      }}
                    ></div>
                  </div>
                ))}
                <svg
                  className="absolute left-0 bottom-0 w-full h-full pointer-events-none"
                  viewBox="0 0 100 100"
                  preserveAspectRatio="none"
                >
                  <polyline
                    className="transition-all duration-[2000ms] ease [stroke-dasharray:180] [stroke-dashoffset:180] group-hover:[stroke-dashoffset:0]"
                    fill="none"
                    stroke="#14b8a6"
                    strokeWidth="2"
                    strokeLinejoin="round"
                    strokeLinecap="round"
                    points="
                      0,55
                      16.66,40
                      33.33,65
                      50,25
                      66.66,50
                      83.33,30
                      100,45
                      "
                    opacity="0.7"
                    style={{
                      animation: 'dash 4s ease-in-out infinite',
                    }}
                  />
                </svg>
                <svg
                  className="absolute left-0 bottom-0 w-full h-full pointer-events-none"
                  viewBox="0 0 100 100"
                  preserveAspectRatio="none"
                >
                  <polyline
                    fill="none"
                    stroke="orange"
                    strokeWidth="2"
                    strokeLinejoin="round"
                    strokeLinecap="round"
                    points="
                      0,55
                      16.66,40
                      33.33,65
                      50,25
                      66.66,50
                      83.33,30
                      100,45
                      "
                    opacity="0.1"
                    style={{
                      animation: 'dash 4s ease-in-out infinite',
                    }}
                  />
                </svg>

                {/* Grid lines */}
                <div className="absolute inset-0 border-t border-dashed border-gray-200 top-1/4 pointer-events-none"></div>
                <div className="absolute inset-0 border-t border-dashed border-gray-200 top-2/4 pointer-events-none"></div>
                <div className="absolute inset-0 border-t border-dashed border-gray-200 top-3/4 pointer-events-none"></div>

                {/* Stats indicators */}
                <div className="absolute -bottom-6 left-0 w-full flex justify-between px-1">
                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(
                    (day) => (
                      <div key={day} className="text-[8px] text-gray-500">
                        {day}
                      </div>
                    ),
                  )}
                </div>
              </div>

              {/* Stats row */}
              <div className="flex gap-3 mt-8">
                <div className="flex-1 h-10 bg-teal-50 rounded px-2 py-1 border border-teal-100 flex flex-col justify-center">
                  <div className="text-[9px] text-gray-500">Conversions</div>
                  <div className="text-[7px] md:text-xs font-semibold text-teal-700">
                    +16.8%
                  </div>
                </div>
                <div className="flex-1 h-10 bg-orange-50 rounded px-2 py-1 border border-orange-100 flex flex-col justify-center">
                  <div className="text-[9px] text-gray-500">Engagement</div>
                  <div className="text-[7px] md:text-xs font-semibold text-orange-600">
                    +24.5%
                  </div>
                </div>
              </div>

              {/* Activity dots */}
              <div className="absolute bottom-3 left-4 flex items-center gap-1.5">
                {[0, 1, 2].map((i) => (
                  <div
                    key={i}
                    className={`
                      size-2 rounded-full transition-all duration-300 bg-teal-200
                      group-hover:bg-teal-500 group-hover:scale-110
                    `}
                  ></div>
                ))}
              </div>
            </div>
            <div className="w-24 border-l border-gray-200 bg-gradient-to-br from-teal-50 to-white flex flex-col items-center justify-center relative">
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-10">
                <div
                  className="absolute size-4 rounded-full bg-teal-300 top-3 left-1 animate-ping"
                  style={{ animationDuration: '3s' }}
                ></div>
                <div
                  className="absolute size-3 rounded-full bg-orange-300 bottom-5 right-2 animate-ping"
                  style={{ animationDuration: '4s', animationDelay: '1s' }}
                ></div>
              </div>
              <div className="w-14 h-14 rounded-full border-4 border-teal-400 flex items-center justify-center relative mb-2 shadow-md mx-auto group">
                <div
                  className="absolute size-10 rounded-full border-2 border-dashed border-teal-200 animate-spin"
                  style={{ animationDuration: '10s' }}
                ></div>
                <BsClock
                  className="text-teal-500 text-3xl animate-spin"
                  style={{ animationDuration: '20s' }}
                />
              </div>

              <div className="text-2xl font-bold text-primary2 italic px-2">
                24/<span className="text-orange-500">7</span>
              </div>
              <div className="text-xxs text-gray-500 mt-1">Always Working</div>

              {/* Time block */}
              <div className="absolute top-3 right-3 bg-white rounded border border-gray-200 px-1.5 py-0.5 text-xxs font-mono text-gray-700 shadow-sm">
                15:42
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OptimizationIllustration;
