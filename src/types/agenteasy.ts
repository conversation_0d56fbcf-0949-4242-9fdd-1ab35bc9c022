export interface IAgentEasyTimestamp {
  _seconds: number;
  _nanoseconds: number;
  toDate?: () => Date;
}

export enum AgentEasyMessageRole {
  ASSISTANT = 'ASSISTANT',
  USER = 'USER',
}

export interface IAgentEasyMessage {
  role: AgentEasyMessageRole;
  time: IAgentEasyTimestamp;
  payload: {
    text?: {
      body: string;
    };
  };
}

export interface IAgentEasyChatResponse {
  data: IAgentEasyMessage[];
}

export interface IAgentEasyClassificationData {
  source: string;
  chat_doc_id: string;
  asked_questions: {
    question: string;
    answer: string;
  }[];
  lead_summary: string;
  lead_category: string;
}

export interface IAgentEasyCampaignContext {
  full_name: string;
  campaign_id: string;
  leadgen_id: string;
  classification_data: IAgentEasyClassificationData;
  chat_status: string;
}

// Last message structure
export interface IAgentEasyLastMessage {
  role: string;
  time: IAgentEasyTimestamp;
  payload: {
    messaging_product: string;
    type: string;
    text: {
      body: string;
    };
    to: string;
  };
}

export interface IAgentEasyConversationDetails {
  created_at: IAgentEasyTimestamp;
  campaign_context: IAgentEasyCampaignContext;
  user_last_reply_time: IAgentEasyTimestamp;
  updated_at: IAgentEasyTimestamp;
  last_message: IAgentEasyLastMessage;
}

export interface IAgentEasyConversationDetailsResponse {
  data: IAgentEasyConversationDetails;
}
