import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/router';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { DASHBOARD_TABS, GLOBALS, QueryParams } from 'src/constants';
import Image from 'next/image';
import classNames from 'classnames';
import { useQuery, useInfiniteQuery } from 'react-query';
import { getCommonHeaders } from 'src/actions';
import Link from 'next/link';
import {
  logApiErrorAndShowToastMessage,
  getLocalStorage,
  setLocalStorage,
} from 'src/utils';
import UserProfileImage from '@/components/lib/UserProfileImage';
import { getUserProfile } from 'src/actions/profile';
import Head from 'next/head';
import { ICampaign } from 'src/types/campaigns';
import ChooseCampaignTypeBs from '@/components/dashboard/ChooseCampaignTypeBs';
import DashboardCampaigns from '@/components/dashboard/DashboardCampaigns';
import DashboardLeads from '@/components/dashboard/DashboardLeads';
import { getV2Campaigns } from 'src/actions/dashboard';
import FetchError from 'src/actions/FetchError';

interface IDashboardProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const SELECTED_TAB_ID_LS_KEY = 'dashboard_selected_tab_id';

const Dashboard = (props: IDashboardProps) => {
  const { user, partnerConfig } = props;

  const [selectedTabId, setSelectedTabId] = useState(DASHBOARD_TABS[0].id);
  const [campaignTypeSelectionBsVisible, setCampaignTypeSelectionBsVisible] =
    useState(false);
  const [campaigns, setCampaigns] = useState<ICampaign[]>([]);

  const router = useRouter();
  const campaignsResponse = useInfiniteQuery(
    'getV2Campaigns',
    (params) => {
      return getV2Campaigns({
        headers: getCommonHeaders(user),
        queryParams: {
          ...(router.query as Record<string, string>),
          [QueryParams.LAST_CURSOR_ID]: (params.pageParam as string) ?? '',
          [QueryParams.LIMIT]: `20`,
        },
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      getNextPageParam: (lastPage) => {
        return lastPage.data?.last_cursor_id || undefined;
      },
      onError: (error: FetchError | Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getV2Campaigns');
      },
    },
  );

  const allCampaigns: ICampaign[] = useMemo(() => {
    const result: ICampaign[] = [];
    campaignsResponse.data?.pages?.forEach((item) => {
      result.push(...item.data.campaigns);
    });
    return result;
  }, [campaignsResponse.data?.pages]);

  // Update campaigns state when data changes
  useEffect(() => {
    setCampaigns(allCampaigns);
  }, [allCampaigns]);

  // Load saved tab selection from localStorage
  useEffect(() => {
    const savedTabId = getLocalStorage(SELECTED_TAB_ID_LS_KEY);
    if (savedTabId && DASHBOARD_TABS.find((tab) => tab.id === savedTabId)) {
      setSelectedTabId(savedTabId);
    }
  }, []);

  useQuery(
    'getUserProfile',
    () => {
      return getUserProfile({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'dashboard.getUserProfile');
      },
      onSuccess: (response) => {
        if (response?.data) {
          GLOBALS.userProfile = response.data;
        }
        // new users when dropped from /edit-profile or when press back in flow (signup -> edit-profile -> back)
        if (!response?.data?.mobile_dial_code) {
          void router.push('/edit-profile?source=login');
        }
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  const renderTab = (tabId: string) => {
    const tab = DASHBOARD_TABS.find((item) => item.id === tabId);
    if (!tab) {
      return null;
    }
    const selected = tabId === selectedTabId;
    return (
      <div
        className="cursor-pointer flex-1"
        onClick={() => {
          setSelectedTabId(tabId);
          setLocalStorage(SELECTED_TAB_ID_LS_KEY, tabId);
        }}
      >
        <div
          className={classNames('h-1', {
            'bg-primary w-full': selected,
          })}
        />
        <div className="py-4 flex flex-col items-center">
          <div>
            <Image
              src={selected ? tab.selectedIconUrl : tab.iconUrl}
              width={tab.iconWidth}
              height={tab.iconHeight}
              alt=""
            />
          </div>
          <p
            className={classNames('mt-2 text-xs', {
              'text-primary font-semibold': selected,
              'text-gray-dark': !selected,
            })}
          >
            {tab.label}
          </p>
        </div>
      </div>
    );
  };

  const onCreateCampaignClick = () => {
    setCampaignTypeSelectionBsVisible(true);
  };

  const handleCampaignsChange = (newCampaigns: ICampaign[]) => {
    setCampaigns(newCampaigns);
  };

  return user ? (
    <MobileContainer>
      <Head>
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <div className="flex flex-col flex-1 h-full w-full pt-4">
        <div className="flex items-center px-5">
          <Link
            className="text-black text-xl font-semibold tracking-tight"
            href="/"
          >
            {partnerConfig?.name ?? 'GrowEasy'}
          </Link>
          <div className="flex-1" />
          <div className="p-3">
            <Link href="/profile">
              <UserProfileImage user={user} width={24} height={24} />
            </Link>
          </div>
        </div>
        <div className="flex-1 overflow-y-scroll flex flex-col no-scrollbar px-5">
          {selectedTabId === DASHBOARD_TABS[0].id ? (
            <DashboardCampaigns
              user={user}
              campaigns={campaigns}
              campaignsResponse={campaignsResponse}
              onCampaignsChange={handleCampaignsChange}
              onCreateCampaignClick={onCreateCampaignClick}
            />
          ) : null}
          {selectedTabId === DASHBOARD_TABS[1].id ? (
            <DashboardLeads
              user={user}
              partnerConfig={partnerConfig}
              campaigns={campaigns}
            />
          ) : null}
        </div>
        <div className="w-full flex items-center bg-white mt-3">
          {renderTab(DASHBOARD_TABS[0].id)}
          <div
            className="flex-1 flex flex-col items-center cursor-pointer"
            onClick={onCreateCampaignClick}
          >
            <div className="w-12 h-12 rounded-full flex items-center justify-center bg-primary -mt-6">
              <Image
                src="/images/dashboard/add.png"
                width="20"
                height="20"
                alt=""
              />
            </div>
            <p className="text-xs text-gray-dark mt-2">New Campaign</p>
          </div>
          {renderTab(DASHBOARD_TABS[1].id)}
        </div>

        {campaignTypeSelectionBsVisible ? (
          <ChooseCampaignTypeBs
            onClose={() => setCampaignTypeSelectionBsVisible(false)}
            partnerConfig={partnerConfig}
            user={user}
          />
        ) : null}
      </div>
    </MobileContainer>
  ) : null;
};

export default Dashboard;
